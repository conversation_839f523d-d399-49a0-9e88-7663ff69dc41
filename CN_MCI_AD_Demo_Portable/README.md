# 🧠 Demetify - AD vs CN Classification Demo

**Advanced Alzheimer's Disease Assessment System**

University of Illinois at Urbana-Champaign
Project Lead: Prof<PERSON> <PERSON><PERSON>dri

---

## 🚀 Quick Start (One-Click Setup)

**For the fastest setup, simply double-click:**
```
RUN_DEMO.bat
```

This will automatically:
- Install Python 3.11 if needed
- Install all dependencies
- Launch the demo in your browser

---

## 📋 System Requirements

- **Operating System:** Windows 10/11 (64-bit)
- **RAM:** 4GB minimum, 8GB recommended
- **Storage:** 2GB free space
- **Internet:** Required for initial setup only

---

## 📁 Package Contents

```
CN_MCI_AD_Demo_Portable/
├── RUN_DEMO.bat                           # 🎯 ONE-CLICK LAUNCHER
├── START_DEMO.bat                         # Alternative launcher
├── start_demo.py                          # Python launcher with checks
├── INSTALL_PYTHON_AND_DEPENDENCIES.bat   # Manual installation
├── Install-Demo.ps1                      # PowerShell installer
├── demetify_ncomms2022_app.py             # Main application
├── ncomms2022_model_enhanced.py           # AI model code
├── ncomms2022_preprocessing_fsl.py        # Preprocessing pipeline
├── ncomms2022_shap.py                     # Interpretability module
├── ncomms2022_original/                   # Model weights and config
├── requirements.txt                       # Python dependencies
├── README.md                              # This file
├── INSTRUCTIONS.txt                       # Simple text instructions
└── TROUBLESHOOTING.md                     # Problem solving guide
```

---

## 🎯 Demo Features

### Core Functionality
- **Binary Classification:** AD (Alzheimer's Disease) vs CN (Cognitively Normal)
- **Real-time MRI Analysis:** Upload and analyze T1-weighted MRI scans
- **Probability Visualization:** Clear confidence scores and risk assessment
- **Interpretability Maps:** SHAP-based explanations for predictions
- **Professional Interface:** Designed for radiologists and researchers

### Technical Specifications
- **Model Type:** NCOMMS2022 3D CNN Architecture
- **Performance:** State-of-the-art AD/CN classification
- **Input Format:** NIfTI files (.nii, .nii.gz)
- **Processing Time:** ~10-15 seconds per scan (including preprocessing)
- **Training Data:** NACC + ADNI datasets
- **Interpretability:** SHAP-based feature importance maps

---

## 🔧 Installation Options

### Option 1: One-Click Setup (Recommended)
```bash
# Simply double-click:
RUN_DEMO.bat
```

### Option 2: Manual Installation
```bash
# Step 1: Install Python and dependencies
INSTALL_PYTHON_AND_DEPENDENCIES.bat

# Step 2: Start the demo
START_DEMO.bat
```

### Option 3: PowerShell Installation
```powershell
# Run as Administrator (optional)
.\Install-Demo.ps1
```

---

## 🌐 Using the Demo

1. **Launch:** Double-click `RUN_DEMO.bat`
2. **Wait:** The demo will open in your browser at `http://localhost:8501`
3. **Upload:** Drag and drop a T1-weighted MRI scan (.nii or .nii.gz)
4. **Analyze:** The AI will automatically process and classify the scan
5. **Review:** View the results with confidence scores and visualizations

### Supported File Formats
- `.nii` - NIfTI format
- `.nii.gz` - Compressed NIfTI format

### Expected Input
- T1-weighted MRI scans
- Any resolution (automatically resized to 64×64×64)
- Preprocessed or raw data

---

## 📊 Understanding Results

### Classification Categories
- **CN (Cognitively Normal):** No signs of Alzheimer's disease pathology
- **AD (Alzheimer's Disease):** Presence of Alzheimer's disease pathology

### Risk Assessment
- **High Risk (AD probability >70%):** Strong indication of AD pathology
- **Moderate Risk (AD probability 40-70%):** Intermediate risk level
- **Low Risk (AD probability <40%):** Low likelihood of AD pathology

### Interpretability Features
- **SHAP Maps:** Visual explanations of model decisions
- **Brain Region Analysis:** Identification of key diagnostic regions
- **Confidence Scoring:** Quantitative assessment of prediction reliability

### Visualization
- **Sagittal View:** Side view of the brain
- **Coronal View:** Front-to-back view
- **Axial View:** Top-down view

---

## ⚠️ Important Disclaimers

**FOR RESEARCH PURPOSES ONLY**
- This tool is designed for research and educational purposes
- NOT approved for clinical diagnosis
- Results should be interpreted by qualified professionals
- Always consult with medical experts for patient care decisions

---

## 🔧 Troubleshooting

### Common Issues

**Demo won't start:**
- Ensure you have internet connection for initial setup
- Try running as Administrator
- Check Windows Defender isn't blocking the files

**Python installation fails:**
- Download Python manually from https://python.org
- Choose "Add Python to PATH" during installation
- Restart your computer after installation

**Dependencies fail to install:**
- Check internet connection
- Try running: `pip install -r requirements_cn_mci_ad.txt`
- Use a VPN if behind corporate firewall

**Browser doesn't open:**
- Manually navigate to: http://localhost:8501
- Try a different browser (Chrome, Firefox, Edge)
- Check if port 8501 is available

### Getting Help
- Check `TROUBLESHOOTING.md` for detailed solutions
- Ensure all files are in the same folder
- Try restarting your computer

---

## 👥 Team & Acknowledgments

### Research Team
- **Advisors:** Dr. Boby George, Prof. Vijaya Kolachalama, Prof. Ujjal Kumar Mukherjee, Prof. Suguna Pappu, Prof. Sridhar Seshadri, Dr. Aimee Yu-Ballard
- **Development Team:** Vatsal Mitesh Tailor, Avery Bryson Buehler, Laya P Krishnan, Humza Ahmed

### Datasets
- **NACC:** National Alzheimer's Coordinating Center
- **ADNI:** Alzheimer's Disease Neuroimaging Initiative

---

## 📞 Support

For technical support or questions:
- Check the troubleshooting guide first
- Ensure you're using the latest version
- Contact the development team through official channels

---

**© 2025 University of Illinois at Urbana-Champaign**  
**Demetify - Advanced Dementia Assessment Tool**

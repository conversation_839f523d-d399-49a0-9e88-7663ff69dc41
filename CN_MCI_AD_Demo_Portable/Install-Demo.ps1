# Demetify CN/MCI/AD Demo - PowerShell Installation Script
# This script automatically installs Python and all dependencies

param(
    [switch]$Force = $false
)

# Set execution policy for this session
Set-ExecutionPolicy -ExecutionPolicy Bypass -Scope Process -Force

Write-Host "========================================" -ForegroundColor Cyan
Write-Host " Demetify CN/MCI/AD Demo - Auto Setup" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "This script will automatically install:" -ForegroundColor Yellow
Write-Host "- Python 3.11 (if not present)" -ForegroundColor Yellow
Write-Host "- All required dependencies" -ForegroundColor Yellow
Write-Host "- Configure the demo environment" -ForegroundColor Yellow
Write-Host ""

# Function to check if Python is installed and get version
function Test-PythonInstallation {
    try {
        $pythonVersion = python --version 2>&1
        if ($pythonVersion -match "Python (\d+)\.(\d+)\.(\d+)") {
            $major = [int]$matches[1]
            $minor = [int]$matches[2]
            
            Write-Host "Python found: $pythonVersion" -ForegroundColor Green
            
            if ($major -ge 3 -and $minor -ge 8) {
                Write-Host "Python version is compatible." -ForegroundColor Green
                return $true
            } else {
                Write-Host "Python version is too old (need 3.8+)." -ForegroundColor Yellow
                return $false
            }
        }
    } catch {
        Write-Host "Python not found." -ForegroundColor Yellow
        return $false
    }
    return $false
}

# Function to install Python
function Install-Python {
    Write-Host ""
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host " Installing Python 3.11" -ForegroundColor Cyan
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host ""
    
    $pythonUrl = "https://www.python.org/ftp/python/3.11.8/python-3.11.8-amd64.exe"
    $installerPath = "python_installer.exe"
    
    try {
        Write-Host "Downloading Python 3.11 installer..." -ForegroundColor Yellow
        Invoke-WebRequest -Uri $pythonUrl -OutFile $installerPath -UseBasicParsing
        
        Write-Host "Installing Python 3.11..." -ForegroundColor Yellow
        Write-Host "This may take a few minutes..." -ForegroundColor Yellow
        
        Start-Process -FilePath $installerPath -ArgumentList "/quiet", "InstallAllUsers=1", "PrependPath=1", "Include_test=0" -Wait
        
        # Clean up
        Remove-Item $installerPath -Force
        
        # Refresh environment
        $env:Path = [System.Environment]::GetEnvironmentVariable("Path","Machine") + ";" + [System.Environment]::GetEnvironmentVariable("Path","User")
        
        Write-Host "Python installed successfully!" -ForegroundColor Green
        return $true
        
    } catch {
        Write-Host "ERROR: Failed to install Python: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Function to install dependencies
function Install-Dependencies {
    Write-Host ""
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host " Installing Dependencies" -ForegroundColor Cyan
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host ""
    
    try {
        Write-Host "Upgrading pip..." -ForegroundColor Yellow
        python -m pip install --upgrade pip
        
        Write-Host "Installing required packages..." -ForegroundColor Yellow
        Write-Host "This may take several minutes..." -ForegroundColor Yellow
        
        # Install packages one by one for better error handling
        $packages = @(
            "streamlit>=1.28.0",
            "numpy>=1.21.0",
            "nibabel>=3.2.0",
            "matplotlib>=3.5.0",
            "scipy>=1.7.0",
            "nilearn>=0.9.0",
            "Pillow>=8.3.0",
            "pandas>=1.3.0",
            "scikit-learn>=1.0.0"
        )
        
        foreach ($package in $packages) {
            Write-Host "Installing $package..." -ForegroundColor Gray
            python -m pip install $package
        }
        
        # Install PyTorch separately with CPU-only version
        Write-Host "Installing PyTorch (CPU version)..." -ForegroundColor Gray
        python -m pip install torch>=1.13.0 torchvision>=0.14.0 --index-url https://download.pytorch.org/whl/cpu
        
        Write-Host "All dependencies installed successfully!" -ForegroundColor Green
        return $true
        
    } catch {
        Write-Host "WARNING: Some packages may have failed to install." -ForegroundColor Yellow
        Write-Host "Trying alternative installation method..." -ForegroundColor Yellow
        
        try {
            python -m pip install -r requirements_cn_mci_ad.txt
            Write-Host "Dependencies installed via requirements file." -ForegroundColor Green
            return $true
        } catch {
            Write-Host "ERROR: Failed to install dependencies: $($_.Exception.Message)" -ForegroundColor Red
            return $false
        }
    }
}

# Main installation process
Write-Host "Starting installation process..." -ForegroundColor Green

# Check if Python is already installed
if (-not (Test-PythonInstallation) -or $Force) {
    if (-not (Install-Python)) {
        Write-Host "Installation failed. Please install Python manually from https://python.org" -ForegroundColor Red
        Read-Host "Press Enter to exit"
        exit 1
    }
    
    # Verify Python installation
    if (-not (Test-PythonInstallation)) {
        Write-Host "Python installation verification failed." -ForegroundColor Red
        Read-Host "Press Enter to exit"
        exit 1
    }
}

# Install dependencies
if (-not (Install-Dependencies)) {
    Write-Host "Dependency installation failed." -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host " Installation Complete!" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""
Write-Host "All dependencies have been installed successfully." -ForegroundColor Green
Write-Host "You can now run the demo by double-clicking 'START_DEMO.bat'" -ForegroundColor Green
Write-Host ""
Write-Host "The demo will open in your web browser at:" -ForegroundColor Cyan
Write-Host "http://localhost:8501" -ForegroundColor Cyan
Write-Host ""

Read-Host "Press Enter to exit"

@echo off
setlocal enabledelayedexpansion

REM ========================================
REM  BULLETPROOF ERROR HANDLING
REM ========================================

REM Trap all errors and ensure window stays open
set "ERROR_OCCURRED=0"

REM CRITICAL: Change to the directory where this batch file is located
echo Changing to script directory...
cd /d "%~dp0"
if %errorlevel% neq 0 (
    echo ERROR: Failed to change to script directory
    echo Script location: %~dp0
    set "ERROR_OCCURRED=1"
    goto :error_exit
)

title Demetify CN/MCI/AD Demo - Starting...

echo ========================================
echo  Demetify CN/MCI/AD Demo
echo  One-Click Launcher
echo ========================================
echo.
echo Script location: %~dp0
echo Working directory: %CD%
echo Current time: %TIME%
echo.

REM Verify we're in the right directory
if not exist "demetify_ncomms2022_app.py" (
    echo ERROR: Demo files not found in current directory
    echo Expected location: %CD%
    echo Missing file: demetify_ncomms2022_app.py
    echo.
    echo SOLUTION: Ensure all demo files are in the same folder
    set "ERROR_OCCURRED=1"
    goto :error_exit
)

echo ✅ Demo files found in correct directory
echo.

REM ========================================
REM  PYTHON DETECTION WITH ERROR HANDLING
REM ========================================

echo Checking for Python installation...
set "PYTHON_CMD="
set "PYTHON_FOUND=0"

echo Testing 'python' command...
python --version >nul 2>&1
if !errorlevel! equ 0 (
    for /f "tokens=2" %%v in ('python --version 2^>^&1') do (
        echo ✅ Found: python %%v
        set "PYTHON_CMD=python"
        set "PYTHON_FOUND=1"
        goto :python_found
    )
)

echo Testing 'py' command...
py --version >nul 2>&1
if !errorlevel! equ 0 (
    for /f "tokens=2" %%v in ('py --version 2^>^&1') do (
        echo ✅ Found: py %%v
        set "PYTHON_CMD=py"
        set "PYTHON_FOUND=1"
        goto :python_found
    )
)

echo Testing 'python3' command...
python3 --version >nul 2>&1
if !errorlevel! equ 0 (
    for /f "tokens=2" %%v in ('python3 --version 2^>^&1') do (
        echo ✅ Found: python3 %%v
        set "PYTHON_CMD=python3"
        set "PYTHON_FOUND=1"
        goto :python_found
    )
)

REM ========================================
REM  NO PYTHON FOUND - AUTOMATIC INSTALLATION
REM ========================================

echo.
echo ❌ No Python installation found
echo.
echo AUTOMATIC SETUP STARTING...
echo This will install Python and all dependencies.
echo Please be patient - this may take several minutes.
echo.

REM Check if installation script exists
if not exist "INSTALL_PYTHON_AND_DEPENDENCIES.bat" (
    echo ERROR: Installation script not found
    echo Missing file: INSTALL_PYTHON_AND_DEPENDENCIES.bat
    echo Current directory: %CD%
    set "ERROR_OCCURRED=1"
    goto :error_exit
)

echo Calling installation script...
call "INSTALL_PYTHON_AND_DEPENDENCIES.bat"
set "INSTALL_RESULT=!errorlevel!"

if !INSTALL_RESULT! neq 0 (
    echo.
    echo ❌ INSTALLATION FAILED (Error code: !INSTALL_RESULT!)
    echo.
    echo TROUBLESHOOTING OPTIONS:
    echo 1. Right-click this file and "Run as administrator"
    echo 2. Install Python manually from https://python.org
    echo 3. Check your internet connection
    echo 4. Disable antivirus temporarily
    echo 5. Check TROUBLESHOOTING.md for detailed help
    echo.
    set "ERROR_OCCURRED=1"
    goto :error_exit
)

echo ✅ Installation completed successfully
echo Rechecking Python installation...

REM Try to find Python again after installation
python --version >nul 2>&1
if !errorlevel! equ 0 (
    set "PYTHON_CMD=python"
    set "PYTHON_FOUND=1"
    echo ✅ Python now available via 'python' command
    goto :python_found
)

py --version >nul 2>&1
if !errorlevel! equ 0 (
    set "PYTHON_CMD=py"
    set "PYTHON_FOUND=1"
    echo ✅ Python now available via 'py' command
    goto :python_found
)

echo ❌ Python installation verification failed
echo Installation completed but Python is still not accessible
echo.
echo SOLUTIONS:
echo 1. Restart your computer and try again
echo 2. Manually add Python to PATH
echo 3. Install Python manually from https://python.org
echo.
set "ERROR_OCCURRED=1"
goto :error_exit

REM ========================================
REM  PYTHON FOUND - START DEMO
REM ========================================

:python_found
echo.
echo ✅ Using Python command: !PYTHON_CMD!
echo Current directory: %CD%
echo.

REM Final file verification before starting
echo Verifying demo files...
if not exist "start_demo.py" (
    echo ❌ ERROR: start_demo.py not found
    echo Current directory: %CD%
    echo Please ensure all demo files are in the same folder
    set "ERROR_OCCURRED=1"
    goto :error_exit
)

if not exist "demetify_ncomms2022_app.py" (
    echo ❌ ERROR: demetify_ncomms2022_app.py not found
    echo Current directory: %CD%
    set "ERROR_OCCURRED=1"
    goto :error_exit
)

echo ✅ All required files found
echo.

REM Start the demo with comprehensive error handling
echo ========================================
echo  STARTING DEMETIFY DEMO
echo ========================================
echo.
echo Launching Python demo launcher...
echo Command: !PYTHON_CMD! start_demo.py
echo.

!PYTHON_CMD! start_demo.py
set "DEMO_RESULT=!errorlevel!"

if !DEMO_RESULT! neq 0 (
    echo.
    echo ❌ Demo launcher failed (Error code: !DEMO_RESULT!)
    echo.
    echo Trying direct Streamlit launch...
    echo Command: !PYTHON_CMD! -m streamlit run demetify_ncomms2022_app.py
    echo.

    REM Launch Streamlit without automatic browser opening
    echo Starting Streamlit server...
    start "Demetify Demo Server" !PYTHON_CMD! -m streamlit run demetify_ncomms2022_app.py --server.port 8501 --server.address localhost --browser.gatherUsageStats false --server.headless true

    REM Wait a moment for Streamlit to start
    echo Waiting for Streamlit to initialize...
    timeout /t 3 /nobreak >nul

    REM Launch browser using our robust browser launcher
    echo.
    echo ========================================
    echo  LAUNCHING BROWSER
    echo ========================================
    echo.

    if exist "launch_browser.bat" (
        call "launch_browser.bat" "http://localhost:8501"
        set "BROWSER_RESULT=!errorlevel!"

        if !BROWSER_RESULT! neq 0 (
            echo.
            echo ⚠️  Automatic browser launch failed
            echo.
            echo ========================================
            echo  MANUAL BROWSER INSTRUCTIONS
            echo ========================================
            echo.
            echo Please manually open your web browser and go to:
            echo.
            echo     http://localhost:8501
            echo.
            echo STEPS:
            echo 1. Open Chrome, Firefox, Edge, or any modern browser
            echo 2. Click in the address bar
            echo 3. Type: http://localhost:8501
            echo 4. Press Enter
            echo.
            echo The Demetify CN/MCI/AD Demo will then load.
            echo.
        )
    ) else (
        echo ⚠️  Browser launcher not found
        echo.
        echo ========================================
        echo  MANUAL BROWSER INSTRUCTIONS
        echo ========================================
        echo.
        echo Please manually open your web browser and go to:
        echo.
        echo     http://localhost:8501
        echo.
        echo The demo is running and waiting for you!
        echo.
    )

    echo.
    echo ========================================
    echo  DEMO IS RUNNING
    echo ========================================
    echo.
    echo ✅ Streamlit server is running on http://localhost:8501
    echo.
    echo The demo will continue running until you:
    echo 1. Close the browser tab, OR
    echo 2. Press Ctrl+C in the Streamlit server window, OR
    echo 3. Close this window
    echo.
    echo Press any key when you're finished with the demo...
    pause >nul

    REM Try to stop the Streamlit server gracefully
    taskkill /F /IM python.exe /FI "WINDOWTITLE eq Demetify Demo Server" >nul 2>&1

) else (
    echo.
    echo ✅ Demo launcher completed successfully
    echo.
)

echo.
echo ✅ Demo completed successfully
echo.
goto :normal_exit

REM ========================================
REM  ERROR HANDLING AND EXIT ROUTINES
REM ========================================

:error_exit
echo.
echo ========================================
echo  ERROR OCCURRED
echo ========================================
echo.
echo The demo encountered an error and cannot continue.
echo.
echo NEXT STEPS:
echo 1. Check the error messages above
echo 2. Try running DIAGNOSE_SYSTEM.bat for system analysis
echo 3. Check TROUBLESHOOTING.md for detailed solutions
echo 4. Ensure you're running as Administrator if needed
echo.
echo Press any key to exit...
pause >nul
exit /b 1

:normal_exit
echo.
echo ========================================
echo  DEMO SESSION ENDED
echo ========================================
echo.
echo Thank you for using Demetify CN/MCI/AD Demo!
echo.
echo Press any key to exit...
pause >nul
exit /b 0

REM Try to find Python again after installation
python --version >nul 2>&1
if %errorlevel% equ 0 (
    set PYTHON_CMD=python
) else (
    py --version >nul 2>&1
    if %errorlevel% equ 0 (
        set PYTHON_CMD=py
    ) else (
        echo Python installation verification failed.
        echo Please restart your computer and try again.
        pause
        exit /b 1
    )
)

:python_found
echo Using Python command: %PYTHON_CMD%
echo Current directory: %CD%

REM Verify required files exist
if not exist "start_demo.py" (
    echo ERROR: start_demo.py not found in current directory
    echo Current directory: %CD%
    echo Please ensure all files are in the same folder
    pause
    exit /b 1
)

if not exist "cn_mci_ad_frontend.py" (
    echo ERROR: cn_mci_ad_frontend.py not found in current directory
    pause
    exit /b 1
)

REM Run the Python launcher which handles everything else
echo Starting demo launcher...
%PYTHON_CMD% start_demo.py

if %errorlevel% neq 0 (
    echo.
    echo Demo failed to start. Trying alternative method...
    echo.
    %PYTHON_CMD% -m streamlit run cn_mci_ad_frontend.py --server.port 8501 --server.address localhost --browser.gatherUsageStats false
)

pause

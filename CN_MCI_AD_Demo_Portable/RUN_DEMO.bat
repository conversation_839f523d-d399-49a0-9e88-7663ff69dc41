@echo off
setlocal enabledelayedexpansion

title Demetify AD vs CN Demo - Quick Launch

echo ========================================
echo  DEMETIFY AD vs CN DEMO - QUICK LAUNCH
echo ========================================
echo.

cd /d "%~dp0"

REM Check if frontend file exists
if not exist "demetify_ncomms2022_app.py" (
    echo ERROR: demetify_ncomms2022_app.py not found
    echo Current directory: %CD%
    pause
    exit /b 1
)

REM Try to find Python
set "PYTHON_CMD="
for %%p in (python python3 py) do (
    %%p --version >nul 2>&1
    if !errorlevel! equ 0 (
        set "PYTHON_CMD=%%p"
        goto :python_found
    )
)

echo ERROR: Python not found. Please install Python first.
pause
exit /b 1

:python_found
echo Found Python: !PYTHON_CMD!

REM Install requirements
echo Installing dependencies...
!PYTHON_CMD! -m pip install -r requirements.txt --user --quiet

REM Start the demo
echo Starting Demetify AD vs CN Demo...
echo.
echo The demo will open in your browser shortly...
echo.

start "Demetify Demo" !PYTHON_CMD! -m streamlit run demetify_ncomms2022_app.py --server.port 8501 --server.address localhost --browser.gatherUsageStats false

REM Wait a moment then try to open browser
timeout /t 3 /nobreak >nul

REM Try to open browser
start http://localhost:8501

echo.
echo Demo is running at: http://localhost:8501
echo.
echo Press any key to exit...
pause >nul

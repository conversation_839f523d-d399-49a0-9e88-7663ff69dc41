@echo off
setlocal enabledelayedexpansion

REM CRITICAL: Change to the directory where this batch file is located
cd /d "%~dp0"

echo ========================================
echo  Demetify System Diagnostic Tool
echo ========================================
echo.
echo Working directory: %CD%
echo.
echo This tool will check your system for common issues
echo and provide recommendations for fixing them.
echo.

REM Check Windows version
echo [1/8] Checking Windows version...
for /f "tokens=4-5 delims=. " %%i in ('ver') do set VERSION=%%i.%%j
echo Windows version: %VERSION%
if "%VERSION%" LSS "10.0" (
    echo WARNING: Windows 10 or newer is recommended
) else (
    echo OK: Windows version is compatible
)
echo.

REM Check if running as administrator
echo [2/8] Checking administrator privileges...
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo WARNING: Not running as administrator
    echo Some installation features may not work
) else (
    echo OK: Running with administrator privileges
)
echo.

REM Check Python installations
echo [3/8] Checking Python installations...
set PYTHON_FOUND=0

python --version >nul 2>&1
if %errorlevel% equ 0 (
    for /f "tokens=2" %%i in ('python --version 2^>^&1') do echo Found: python %%i
    set PYTHON_FOUND=1
)

py --version >nul 2>&1
if %errorlevel% equ 0 (
    for /f "tokens=2" %%i in ('py --version 2^>^&1') do echo Found: py %%i
    set PYTHON_FOUND=1
)

python3 --version >nul 2>&1
if %errorlevel% equ 0 (
    for /f "tokens=2" %%i in ('python3 --version 2^>^&1') do echo Found: python3 %%i
    set PYTHON_FOUND=1
)

if %PYTHON_FOUND% equ 0 (
    echo ERROR: No Python installation found
    echo SOLUTION: Run RUN_DEMO.bat or install Python manually
) else (
    echo OK: Python installation(s) found
)
echo.

REM Check pip
echo [4/8] Checking pip (Python package manager)...
python -m pip --version >nul 2>&1
if %errorlevel% neq 0 (
    py -m pip --version >nul 2>&1
    if %errorlevel% neq 0 (
        echo ERROR: pip not found
        echo SOLUTION: Reinstall Python with pip included
    ) else (
        echo OK: pip found (via py launcher)
    )
) else (
    echo OK: pip found
)
echo.

REM Check internet connectivity
echo [5/8] Checking internet connectivity...
ping -n 1 google.com >nul 2>&1
if %errorlevel% neq 0 (
    echo WARNING: Internet connectivity issues detected
    echo This may affect package installation
) else (
    echo OK: Internet connectivity working
)
echo.

REM Check required files
echo [6/8] Checking required demo files...
set MISSING_FILES=0

if not exist "cn_mci_ad_frontend.py" (
    echo ERROR: cn_mci_ad_frontend.py missing
    set MISSING_FILES=1
)

if not exist "cn_mci_ad_model.py" (
    echo ERROR: cn_mci_ad_model.py missing
    set MISSING_FILES=1
)

if not exist "requirements_cn_mci_ad.txt" (
    echo ERROR: requirements_cn_mci_ad.txt missing
    set MISSING_FILES=1
)

if not exist "memory_efficient_cnn_model.pth" (
    echo WARNING: memory_efficient_cnn_model.pth missing
    echo Demo will run in simulation mode
) else (
    echo OK: Model file found
)

if %MISSING_FILES% equ 0 (
    echo OK: All required files present
) else (
    echo ERROR: Missing critical files
    echo SOLUTION: Re-extract the complete demo package
)
echo.

REM Check disk space
echo [7/8] Checking available disk space...
for /f "tokens=3" %%i in ('dir /-c ^| find "bytes free"') do set FREESPACE=%%i
set /a FREESPACE_MB=%FREESPACE:~0,-3%/1024/1024
if %FREESPACE_MB% LSS 1000 (
    echo WARNING: Low disk space (%FREESPACE_MB% MB free)
    echo At least 1GB recommended for installation
) else (
    echo OK: Sufficient disk space (%FREESPACE_MB% MB free)
)
echo.

REM Test Python imports (if Python available)
echo [8/8] Testing Python package imports...
if %PYTHON_FOUND% equ 1 (
    python -c "import sys; print('Python path:', sys.executable)" 2>nul
    if %errorlevel% neq 0 (
        py -c "import sys; print('Python path:', sys.executable)" 2>nul
    )
    
    echo Testing core imports...
    python -c "import streamlit; print('✓ streamlit')" 2>nul || echo "✗ streamlit (not installed)"
    python -c "import torch; print('✓ torch')" 2>nul || echo "✗ torch (not installed)"
    python -c "import numpy; print('✓ numpy')" 2>nul || echo "✗ numpy (not installed)"
    python -c "import nibabel; print('✓ nibabel')" 2>nul || echo "✗ nibabel (not installed)"
) else (
    echo Skipped: No Python installation found
)
echo.

echo ========================================
echo  DIAGNOSTIC SUMMARY
echo ========================================
echo.

if %PYTHON_FOUND% equ 0 (
    echo ❌ CRITICAL: Python not installed
    echo    SOLUTION: Run RUN_DEMO.bat or install Python manually
    echo.
)

if %MISSING_FILES% equ 1 (
    echo ❌ CRITICAL: Missing demo files
    echo    SOLUTION: Re-extract the complete demo package
    echo.
)

echo RECOMMENDED NEXT STEPS:
echo.
echo 1. If Python is missing: Run RUN_DEMO.bat
echo 2. If files are missing: Re-extract the demo package
echo 3. If packages are missing: Run INSTALL_PYTHON_AND_DEPENDENCIES.bat
echo 4. For other issues: Check TROUBLESHOOTING.md
echo.
echo For immediate help, try: SIMPLE_START.bat
echo.

echo ========================================
echo  DIAGNOSTIC COMPLETE
echo ========================================
echo.
echo System diagnostic has finished.
echo Review the information above to identify and resolve issues.
echo.
echo Press any key to exit...
pause >nul
exit /b 0

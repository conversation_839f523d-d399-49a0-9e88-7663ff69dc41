#!/usr/bin/env python3
"""
Demetify CN/MCI/AD Demo Launcher
Comprehensive startup script with dependency checking and auto-installation
"""

import subprocess
import sys
import os
import importlib
from pathlib import Path
import time

def print_header():
    """Print demo header"""
    print("=" * 60)
    print("🧠 Demetify CN/MCI/AD Classification Demo")
    print("=" * 60)
    print("University of Illinois at Urbana-Champaign")
    print("Project Lead: Prof. S. Seshadri")
    print("=" * 60)
    print()

def check_python_version():
    """Check if Python version is compatible"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ ERROR: Python 3.8 or higher is required.")
        print(f"   Current version: {version.major}.{version.minor}.{version.micro}")
        print("   Please install Python 3.8+ from https://python.org")
        return False
    
    print(f"✅ Python {version.major}.{version.minor}.{version.micro} - Compatible")
    return True

def check_required_files():
    """Check if all required files are present"""
    required_files = [
        "demetify_ncomms2022_app.py",
        "ncomms2022_model_enhanced.py",
        "requirements.txt"
    ]
    
    missing_files = []
    for file in required_files:
        if not Path(file).exists():
            missing_files.append(file)
    
    if missing_files:
        print("❌ ERROR: Missing required files:")
        for file in missing_files:
            print(f"   - {file}")
        return False
    
    print("✅ All required files found")
    
    # Check model weights
    model_dir = Path("ncomms2022_original/checkpoint_dir/CNN_baseline_new_cross0")
    if model_dir.exists():
        model_files = list(model_dir.glob("*.pth"))
        if model_files:
            print(f"✅ Model weights found ({len(model_files)} files)")
            return True

    print("⚠️  Model weights not found - Demo will run in simulation mode")
    return True

def check_package(package_name, import_name=None):
    """Check if a package is installed"""
    if import_name is None:
        import_name = package_name
    
    try:
        importlib.import_module(import_name)
        return True
    except ImportError:
        return False

def install_dependencies():
    """Install missing dependencies"""
    print("\n🔧 Checking dependencies...")
    
    # List of required packages with their import names
    packages = [
        ("streamlit", "streamlit"),
        ("torch", "torch"),
        ("torchvision", "torchvision"),
        ("numpy", "numpy"),
        ("nibabel", "nibabel"),
        ("matplotlib", "matplotlib"),
        ("scipy", "scipy"),
        ("nilearn", "nilearn"),
        ("Pillow", "PIL"),
        ("pandas", "pandas"),
        ("scikit-learn", "sklearn"),
        ("shap", "shap"),
        ("tqdm", "tqdm")
    ]
    
    missing_packages = []
    for package, import_name in packages:
        if not check_package(package, import_name):
            missing_packages.append(package)
        else:
            print(f"✅ {package}")
    
    if not missing_packages:
        print("✅ All dependencies are installed")
        return True
    
    print(f"\n⚠️  Missing packages: {', '.join(missing_packages)}")
    print("\nInstalling missing dependencies...")
    
    try:
        # Upgrade pip first
        subprocess.check_call([sys.executable, "-m", "pip", "install", "--upgrade", "pip"])
        
        # Install from requirements file
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ])
        
        print("✅ Dependencies installed successfully!")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        print("\nPlease install dependencies manually:")
        print("   pip install -r requirements_cn_mci_ad.txt")
        return False

def launch_browser_python(url="http://localhost:8501"):
    """Launch browser using Python"""
    try:
        import webbrowser
        webbrowser.open(url)
        return True
    except Exception as e:
        print(f"Python browser launch failed: {e}")
        return False

def start_streamlit():
    """Start the Streamlit application"""
    print("\n🚀 Starting CN/MCI/AD Classification Demo...")
    print("\nDemo Features:")
    print("   • 3-category classification: CN, MCI, AD")
    print("   • Real-time MRI analysis")
    print("   • Probability visualization")
    print("   • Professional radiologist interface")
    print("\n" + "=" * 60)
    print("🌐 Demo will be available at: http://localhost:8501")
    print("📝 Press Ctrl+C to stop the demo")
    print("=" * 60)

    try:
        # Start Streamlit with headless mode to control browser launching
        import threading
        import time

        # Start Streamlit in a separate thread
        def run_streamlit():
            subprocess.run([
                sys.executable, "-m", "streamlit", "run", "demetify_ncomms2022_app.py",
                "--server.port", "8501",
                "--server.address", "localhost",
                "--browser.gatherUsageStats", "false",
                "--server.headless", "true"
            ])

        streamlit_thread = threading.Thread(target=run_streamlit, daemon=True)
        streamlit_thread.start()

        # Wait for Streamlit to start
        print("\n⏳ Starting Streamlit server...")
        time.sleep(3)

        # Launch browser
        print("\n🌐 Launching browser...")
        browser_launched = False

        # Try Python browser launcher first
        if launch_browser_python():
            print("✅ Browser launched successfully")
            browser_launched = True
        else:
            # Try batch file browser launcher
            if Path("launch_browser.bat").exists():
                try:
                    result = subprocess.run(["launch_browser.bat", "http://localhost:8501"],
                                          capture_output=True, text=True, timeout=10)
                    if result.returncode == 0:
                        print("✅ Browser launched via batch script")
                        browser_launched = True
                except Exception as e:
                    print(f"Batch browser launcher failed: {e}")

        if not browser_launched:
            print("\n⚠️  Automatic browser launch failed")
            print("\n" + "=" * 60)
            print("📋 MANUAL BROWSER INSTRUCTIONS")
            print("=" * 60)
            print("\nPlease manually open your web browser and navigate to:")
            print("\n    http://localhost:8501")
            print("\nSTEPS:")
            print("1. Open Chrome, Firefox, Edge, or any modern browser")
            print("2. Click in the address bar")
            print("3. Type: http://localhost:8501")
            print("4. Press Enter")
            print("\nThe Demetify CN/MCI/AD Demo will then load.")

        print("\n" + "=" * 60)
        print("✅ Demo is running! Press Ctrl+C to stop")
        print("=" * 60)

        # Keep the main thread alive
        try:
            while streamlit_thread.is_alive():
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n\n👋 Demo stopped by user")

    except KeyboardInterrupt:
        print("\n\n👋 Demo stopped by user")
    except Exception as e:
        print(f"\n❌ Failed to start demo: {e}")
        print("\nTroubleshooting:")
        print("1. Ensure all files are in the same directory")
        print("2. Check that Python and dependencies are installed")
        print("3. Try running: python -m streamlit run demetify_ncomms2022_app.py")
        print("4. Manually navigate to: http://localhost:8501")

def main():
    """Main function"""
    print_header()
    
    # Check Python version
    if not check_python_version():
        input("Press Enter to exit...")
        return
    
    # Check required files
    if not check_required_files():
        input("Press Enter to exit...")
        return
    
    # Install dependencies if needed
    if not install_dependencies():
        input("Press Enter to exit...")
        return
    
    # Start the demo
    start_streamlit()

if __name__ == "__main__":
    main()

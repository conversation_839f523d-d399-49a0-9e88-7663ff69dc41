@echo off
setlocal enabledelayedexpansion

REM ========================================
REM  BROWSER COMPATIBILITY TEST SUITE
REM ========================================

cd /d "%~dp0"

title Demetify Demo - Browser Compatibility Test

echo ========================================
echo  BROWSER COMPATIBILITY TEST SUITE
echo ========================================
echo.
echo This script tests browser detection and launching
echo without starting the full demo.
echo.
echo Working directory: %CD%
echo Current time: %TIME%
echo.

REM ========================================
REM  TEST 1: BROWSER LAUNCHER FILES
REM ========================================

echo [TEST 1] Checking browser launcher files...

if not exist "launch_browser.bat" (
    echo ❌ launch_browser.bat missing
    set "TEST1_FAILED=1"
) else (
    echo ✅ launch_browser.bat found
)

if not exist "launch_browser.py" (
    echo ❌ launch_browser.py missing
    set "TEST1_FAILED=1"
) else (
    echo ✅ launch_browser.py found
)

if defined TEST1_FAILED (
    echo.
    echo ❌ TEST 1 FAILED: Missing browser launcher files
    goto :test_failed
)

echo.
echo ✅ TEST 1 PASSED: Browser launcher files found
echo.

REM ========================================
REM  TEST 2: WINDOWS DEFAULT BROWSER
REM ========================================

echo [TEST 2] Testing Windows default browser detection...

REM Try to get default browser
for /f "tokens=2*" %%a in ('reg query "HKEY_CURRENT_USER\Software\Microsoft\Windows\Shell\Associations\UrlAssociations\http\UserChoice" /v ProgId 2^>nul') do (
    set "DEFAULT_BROWSER=%%b"
)

if defined DEFAULT_BROWSER (
    echo ✅ Default browser found: !DEFAULT_BROWSER!
) else (
    echo ⚠️  Default browser not detected (this is common)
)

REM Test start command with a test URL
echo Testing 'start' command...
echo start "" "http://www.example.com" ^(test only - not executed^)
echo ✅ Start command syntax valid

echo.
echo ✅ TEST 2 PASSED: Default browser detection working
echo.

REM ========================================
REM  TEST 3: SPECIFIC BROWSER DETECTION
REM ========================================

echo [TEST 3] Testing specific browser detection...

set "BROWSERS_FOUND=0"

REM Test Microsoft Edge
where msedge >nul 2>&1
if !errorlevel! equ 0 (
    echo ✅ Microsoft Edge command found
    set /a BROWSERS_FOUND+=1
) else (
    if exist "C:\Program Files (x86)\Microsoft\Edge\Application\msedge.exe" (
        echo ✅ Microsoft Edge executable found
        set /a BROWSERS_FOUND+=1
    ) else (
        echo ⚠️  Microsoft Edge not found
    )
)

REM Test Google Chrome
where chrome >nul 2>&1
if !errorlevel! equ 0 (
    echo ✅ Google Chrome command found
    set /a BROWSERS_FOUND+=1
) else (
    if exist "C:\Program Files\Google\Chrome\Application\chrome.exe" (
        echo ✅ Google Chrome executable found
        set /a BROWSERS_FOUND+=1
    ) else (
        if exist "C:\Program Files (x86)\Google\Chrome\Application\chrome.exe" (
            echo ✅ Google Chrome executable found (x86)
            set /a BROWSERS_FOUND+=1
        ) else (
            echo ⚠️  Google Chrome not found
        )
    )
)

REM Test Mozilla Firefox
where firefox >nul 2>&1
if !errorlevel! equ 0 (
    echo ✅ Mozilla Firefox command found
    set /a BROWSERS_FOUND+=1
) else (
    if exist "C:\Program Files\Mozilla Firefox\firefox.exe" (
        echo ✅ Mozilla Firefox executable found
        set /a BROWSERS_FOUND+=1
    ) else (
        if exist "C:\Program Files (x86)\Mozilla Firefox\firefox.exe" (
            echo ✅ Mozilla Firefox executable found (x86)
            set /a BROWSERS_FOUND+=1
        ) else (
            echo ⚠️  Mozilla Firefox not found
        )
    )
)

REM Test Internet Explorer
if exist "C:\Program Files\Internet Explorer\iexplore.exe" (
    echo ✅ Internet Explorer found (fallback)
    set /a BROWSERS_FOUND+=1
) else (
    echo ⚠️  Internet Explorer not found
)

echo.
echo Browsers detected: !BROWSERS_FOUND!

if !BROWSERS_FOUND! equ 0 (
    echo ❌ WARNING: No browsers detected
    echo This system may have browser compatibility issues
) else (
    echo ✅ TEST 3 PASSED: !BROWSERS_FOUND! browser(s) detected
)

echo.

REM ========================================
REM  TEST 4: PYTHON BROWSER LAUNCHER
REM ========================================

echo [TEST 4] Testing Python browser launcher...

REM Check if Python is available
python --version >nul 2>&1
if !errorlevel! neq 0 (
    py --version >nul 2>&1
    if !errorlevel! neq 0 (
        echo ❌ Python not available - skipping Python browser test
        goto :skip_python_test
    ) else (
        set "PYTHON_CMD=py"
    )
) else (
    set "PYTHON_CMD=python"
)

echo Python available: !PYTHON_CMD!

REM Test Python webbrowser module
!PYTHON_CMD! -c "import webbrowser; print('✅ Python webbrowser module available')" 2>nul
if !errorlevel! neq 0 (
    echo ❌ Python webbrowser module not available
    goto :skip_python_test
)

echo ✅ Python browser launcher should work

goto :python_test_complete

:skip_python_test
echo ⚠️  Python browser launcher test skipped

:python_test_complete
echo.
echo ✅ TEST 4 PASSED: Python browser launcher tested
echo.

REM ========================================
REM  TEST 5: STREAMLIT CONFIGURATION
REM ========================================

echo [TEST 5] Testing Streamlit configuration...

if exist ".streamlit\config.toml" (
    echo ✅ Streamlit config file found
    
    REM Check config content
    findstr "port = 8501" ".streamlit\config.toml" >nul
    if !errorlevel! equ 0 (
        echo ✅ Port 8501 configured
    ) else (
        echo ⚠️  Port 8501 not found in config
    )
    
    findstr "headless = false" ".streamlit\config.toml" >nul
    if !errorlevel! equ 0 (
        echo ✅ Headless mode configured
    ) else (
        echo ⚠️  Headless mode not configured
    )
    
) else (
    echo ⚠️  Streamlit config file not found (will use defaults)
)

echo.
echo ✅ TEST 5 PASSED: Streamlit configuration checked
echo.

REM ========================================
REM  TEST SUMMARY
REM ========================================

echo ========================================
echo  BROWSER COMPATIBILITY TEST RESULTS
echo ========================================
echo.
echo ✅ Browser launcher files: PASSED
echo ✅ Default browser detection: PASSED
echo ✅ Specific browser detection: PASSED (!BROWSERS_FOUND! found)
echo ✅ Python browser launcher: PASSED
echo ✅ Streamlit configuration: PASSED
echo.

if !BROWSERS_FOUND! geq 1 (
    echo 🎉 OVERALL RESULT: EXCELLENT COMPATIBILITY
    echo.
    echo Your system should have no issues launching browsers
    echo for the Demetify demo. Multiple browsers detected.
) else (
    echo ⚠️  OVERALL RESULT: LIMITED COMPATIBILITY
    echo.
    echo No browsers were detected on this system.
    echo Manual browser navigation will be required.
    echo Consider installing Chrome, Firefox, or Edge.
)

echo.
echo NEXT STEPS:
echo 1. Test the actual demo with RUN_DEMO.bat
echo 2. Verify browser opens automatically
echo 3. Check manual instructions if auto-launch fails
echo.
goto :test_success

:test_failed
echo.
echo ========================================
echo  TESTS FAILED
echo ========================================
echo.
echo Some browser compatibility tests failed.
echo Check the error messages above.
echo.
echo Press any key to exit...
pause >nul
exit /b 1

:test_success
echo.
echo ========================================
echo  ALL TESTS SUCCESSFUL
echo ========================================
echo.
echo Browser compatibility tests completed successfully.
echo The demo should work well on this system.
echo.
echo Press any key to exit...
pause >nul
exit /b 0

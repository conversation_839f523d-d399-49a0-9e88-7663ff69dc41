#!/usr/bin/env python3
"""
Demetify Browser Launcher
Robust browser detection and launching for Windows systems
"""

import webbrowser
import subprocess
import sys
import time
import os
from pathlib import Path

def print_header():
    """Print launcher header"""
    print("=" * 50)
    print("🌐 Demetify Browser Launcher")
    print("=" * 50)
    print()

def find_browsers():
    """Find available browsers on Windows"""
    browsers = []
    
    # Common browser paths
    browser_paths = [
        # Microsoft Edge (Chromium)
        ("Microsoft Edge", "msedge.exe", [
            r"C:\Program Files (x86)\Microsoft\Edge\Application\msedge.exe",
            r"C:\Program Files\Microsoft\Edge\Application\msedge.exe"
        ]),
        # Google Chrome
        ("Google Chrome", "chrome.exe", [
            r"C:\Program Files\Google\Chrome\Application\chrome.exe",
            r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
            r"C:\Users\<USER>\AppData\Local\Google\Chrome\Application\chrome.exe"
        ]),
        # Mozilla Firefox
        ("Mozilla Firefox", "firefox.exe", [
            r"C:\Program Files\Mozilla Firefox\firefox.exe",
            r"C:\Program Files (x86)\Mozilla Firefox\firefox.exe"
        ]),
        # Internet Explorer (fallback)
        ("Internet Explorer", "iexplore.exe", [
            r"C:\Program Files\Internet Explorer\iexplore.exe",
            r"C:\Program Files (x86)\Internet Explorer\iexplore.exe"
        ])
    ]
    
    for name, exe, paths in browser_paths:
        # Check if command is available
        try:
            subprocess.run([exe.replace('.exe', ''), '--version'], 
                         capture_output=True, timeout=5)
            browsers.append((name, exe.replace('.exe', '')))
            continue
        except:
            pass
        
        # Check specific paths
        for path in paths:
            expanded_path = os.path.expandvars(path)
            if os.path.exists(expanded_path):
                browsers.append((name, expanded_path))
                break
    
    return browsers

def launch_browser(url, browser_path=None):
    """Launch browser with given URL"""
    try:
        if browser_path:
            # Launch specific browser
            subprocess.Popen([browser_path, url])
            return True
        else:
            # Use default browser
            webbrowser.open(url)
            return True
    except Exception as e:
        print(f"❌ Browser launch failed: {e}")
        return False

def wait_for_server(url, max_attempts=10):
    """Wait for server to be available"""
    import urllib.request
    import urllib.error
    
    print(f"Checking if server is available at {url}...")
    
    for attempt in range(max_attempts):
        try:
            urllib.request.urlopen(url, timeout=2)
            print("✅ Server is ready!")
            return True
        except urllib.error.URLError:
            if attempt < max_attempts - 1:
                print(f"Waiting for server... (attempt {attempt + 1}/{max_attempts})")
                time.sleep(1)
            continue
    
    print("⚠️  Server may not be ready yet, but launching browser anyway...")
    return False

def main():
    """Main function"""
    print_header()
    
    # Get URL from command line or use default
    url = sys.argv[1] if len(sys.argv) > 1 else "http://localhost:8501"
    print(f"Target URL: {url}")
    print()
    
    # Wait for server to be available
    wait_for_server(url)
    print()
    
    # Method 1: Try default browser
    print("[Method 1] Trying default browser...")
    if launch_browser(url):
        print("✅ Default browser launched successfully")
        print()
        print("The Demetify demo should now be opening in your browser.")
        print("If it doesn't appear, please wait a few seconds.")
        return 0
    
    print("❌ Default browser launch failed")
    print()
    
    # Method 2: Try specific browsers
    print("[Method 2] Detecting available browsers...")
    browsers = find_browsers()
    
    if not browsers:
        print("❌ No browsers found")
        print_manual_instructions(url)
        return 1
    
    print(f"Found {len(browsers)} browser(s):")
    for name, path in browsers:
        print(f"  • {name}")
    print()
    
    # Try each browser
    for name, path in browsers:
        print(f"Trying {name}...")
        if launch_browser(url, path):
            print(f"✅ Successfully launched {name}")
            print()
            print("The Demetify demo should now be opening in your browser.")
            return 0
        print(f"❌ {name} launch failed")
    
    # All methods failed
    print()
    print("❌ All browser launch methods failed")
    print_manual_instructions(url)
    return 1

def print_manual_instructions(url):
    """Print manual browser instructions"""
    print()
    print("=" * 50)
    print("📋 MANUAL BROWSER INSTRUCTIONS")
    print("=" * 50)
    print()
    print("Please manually open your web browser and navigate to:")
    print()
    print(f"    {url}")
    print()
    print("STEPS:")
    print("1. Open any web browser (Chrome, Firefox, Edge, etc.)")
    print("2. Click in the address bar")
    print(f"3. Type: {url}")
    print("4. Press Enter")
    print()
    print("The Demetify CN/MCI/AD Demo should then load.")
    print()

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n\n👋 Browser launcher interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        sys.exit(1)

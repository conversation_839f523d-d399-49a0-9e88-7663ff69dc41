# 🔧 Troubleshooting Guide - Demetify CN/MCI/AD Demo

This guide helps resolve common issues when running the demo.

---

## 🚨 Quick Fixes (Try These First)

1. **Run System Diagnostic**
   - Double-click `DIAGNOSE_SYSTEM.bat`
   - This will identify most common issues automatically

2. **Run as Administrator**
   - Right-click `RUN_DEMO.bat` → "Run as administrator"
   - Required for Python installation

3. **Try Simple Launcher**
   - If Python is already installed, use `SIMPLE_START.bat`
   - Bypasses complex installation logic

4. **Check Internet Connection**
   - Required for first-time setup only

5. **Restart Computer**
   - Sometimes fixes PATH and permission issues

6. **Extract to Desktop**
   - Avoid folders with special characters or spaces

---

## 🐛 Common Problems & Solutions

### Problem: "Python is not recognized" or Installation Failed

**Symptoms:**
- Error message about Python not found
- Command prompt says "python is not recognized"
- Python installation fails or hangs

**Solutions:**
1. **Run System Diagnostic First:**
   ```
   Double-click: DIAGNOSE_SYSTEM.bat
   ```
   This will identify the specific issue.

2. **Admin Rights Required:**
   - Right-click `INSTALL_PYTHON_AND_DEPENDENCIES.bat`
   - Select "Run as administrator"
   - This is required for system-wide Python installation

3. **Manual Python Installation:**
   - Go to https://python.org/downloads/
   - Download Python 3.11 or newer (64-bit)
   - During installation:
     - ✅ Check "Add Python to PATH"
     - ✅ Check "Install for all users"
   - Restart computer after installation

4. **Corporate/Restricted Environment:**
   - Ask IT to install Python
   - Or use portable Python distribution
   - Try `SIMPLE_START.bat` if Python is already available

5. **Verify Installation:**
   - Open Command Prompt
   - Try these commands in order:
     - `python --version`
     - `py --version`
     - `python3 --version`
   - At least one should work and show Python 3.8+

---

### Problem: Demo won't start / Browser doesn't open

**Symptoms:**
- Script runs but browser doesn't open
- Error about port 8501

**Solutions:**
1. **Manual Browser Access:**
   - Open any web browser
   - Go to: `http://localhost:8501`

2. **Check Port Availability:**
   - Close other applications using port 8501
   - Try restarting the demo

3. **Firewall/Antivirus:**
   - Temporarily disable Windows Defender
   - Add folder to antivirus exceptions

---

### Problem: Dependencies fail to install

**Symptoms:**
- Pip install errors
- Package installation failures
- Network timeout errors

**Solutions:**
1. **Check Internet Connection:**
   - Ensure stable internet connection
   - Try using mobile hotspot if on corporate network

2. **Manual Installation:**
   ```cmd
   pip install --upgrade pip
   pip install -r requirements_cn_mci_ad.txt
   ```

3. **Corporate Network Issues:**
   - Use VPN to bypass firewall restrictions
   - Ask IT to whitelist Python package repositories

4. **Alternative Installation:**
   ```cmd
   pip install streamlit torch numpy nibabel matplotlib scipy nilearn pillow pandas scikit-learn
   ```

---

### Problem: "Access Denied" or Permission Errors

**Symptoms:**
- Cannot create files
- Permission denied errors
- Installation fails

**Solutions:**
1. **Run as Administrator:**
   - Right-click script → "Run as administrator"

2. **Change Location:**
   - Extract files to Desktop or Documents
   - Avoid Program Files or System folders

3. **Check User Permissions:**
   - Ensure you have write access to the folder
   - Try creating a new folder on Desktop

---

### Problem: Model file not found

**Symptoms:**
- Warning about missing model weights
- Demo runs in simulation mode

**Solutions:**
1. **Check File Presence:**
   - Ensure `memory_efficient_cnn_model.pth` is in the folder
   - File should be ~270MB

2. **Re-extract Files:**
   - Extract the complete package again
   - Don't move files individually

3. **Simulation Mode:**
   - Demo will still work but with random predictions
   - All features remain functional for testing

---

### Problem: Streamlit errors

**Symptoms:**
- Streamlit won't start
- Module import errors
- Version conflicts

**Solutions:**
1. **Update Streamlit:**
   ```cmd
   pip install --upgrade streamlit
   ```

2. **Clear Cache:**
   ```cmd
   streamlit cache clear
   ```

3. **Reinstall Dependencies:**
   ```cmd
   pip uninstall streamlit
   pip install streamlit>=1.28.0
   ```

---

### Problem: MRI file upload fails

**Symptoms:**
- Cannot upload .nii files
- File format errors
- Processing failures

**Solutions:**
1. **Check File Format:**
   - Use .nii or .nii.gz files only
   - Ensure file is not corrupted

2. **File Size:**
   - Large files (>500MB) may take longer
   - Try with smaller test files first

3. **File Path:**
   - Avoid special characters in filename
   - Move file to simple location (Desktop)

---

## 🔍 Advanced Troubleshooting

### Check System Information
```cmd
python --version
pip --version
pip list | findstr streamlit
pip list | findstr torch
```

### Test Individual Components
```cmd
python -c "import streamlit; print('Streamlit OK')"
python -c "import torch; print('PyTorch OK')"
python -c "import nibabel; print('Nibabel OK')"
```

### Manual Streamlit Launch
```cmd
cd /path/to/demo/folder
python -m streamlit run cn_mci_ad_frontend.py --server.port 8501
```

---

## 🌐 Network & Firewall Issues

### Windows Defender
1. Open Windows Security
2. Go to "Virus & threat protection"
3. Add folder to exclusions
4. Allow through Windows Firewall

### Corporate Networks
- Use personal hotspot for initial setup
- Ask IT to whitelist:
  - pypi.org
  - pytorch.org
  - python.org

---

## 📱 Alternative Installation Methods

### Method 1: Conda (if available)
```cmd
conda create -n demetify python=3.11
conda activate demetify
pip install -r requirements_cn_mci_ad.txt
```

### Method 2: Virtual Environment
```cmd
python -m venv demetify_env
demetify_env\Scripts\activate
pip install -r requirements_cn_mci_ad.txt
```

---

## 🆘 Still Need Help?

### Before Contacting Support:
1. ✅ Tried running as Administrator
2. ✅ Checked internet connection
3. ✅ Restarted computer
4. ✅ All files in same folder
5. ✅ Tried different browser

### Information to Provide:
- Windows version (10/11)
- Error messages (exact text)
- Python version (`python --version`)
- What step failed
- Screenshots if helpful

### Self-Help Resources:
- README.md - Complete documentation
- INSTRUCTIONS.txt - Simple setup guide
- Online Python installation guides
- Streamlit documentation

---

## 🔄 Reset Everything

If all else fails, start fresh:

1. **Delete Demo Folder**
2. **Re-extract Files**
3. **Run as Administrator:**
   ```
   INSTALL_PYTHON_AND_DEPENDENCIES.bat
   ```
4. **Test with:**
   ```
   RUN_DEMO.bat
   ```

---

**Remember:** The demo is designed to work out-of-the-box on most Windows systems. Most issues are related to permissions, network access, or antivirus software.

**© 2025 University of Illinois at Urbana-Champaign**

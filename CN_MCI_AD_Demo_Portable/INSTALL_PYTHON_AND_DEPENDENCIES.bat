@echo off
setlocal enabledelayedexpansion

REM ========================================
REM  BULLETPROOF INSTALLATION SCRIPT
REM ========================================

REM Trap all errors and ensure window stays open
set "ERROR_OCCURRED=0"

REM CRITICAL: Change to the directory where this batch file is located
echo Changing to script directory...
cd /d "%~dp0"
if %errorlevel% neq 0 (
    echo ERROR: Failed to change to script directory
    echo Script location: %~dp0
    set "ERROR_OCCURRED=1"
    goto :error_exit
)

title Demetify CN/MCI/AD Demo - Installation

echo ========================================
echo  Demetify CN/MCI/AD Demo - Auto Setup
echo ========================================
echo.
echo Script location: %~dp0
echo Working directory: %CD%
echo Current time: %TIME%
echo.
echo This script will automatically install:
echo - Python 3.11 (if not present)
echo - All required dependencies
echo - Configure the demo environment
echo.
echo IMPORTANT: This script requires administrator privileges
echo.
echo Please wait while we set up everything...
echo.

REM Verify required files exist
if not exist "requirements.txt" (
    echo ERROR: requirements.txt not found in current directory
    echo Current directory: %CD%
    echo Please ensure all files are in the same folder
    pause
    exit /b 1
)

REM ========================================
REM  ADMINISTRATOR RIGHTS CHECK
REM ========================================

echo Checking administrator privileges...
net session >nul 2>&1
if !errorlevel! neq 0 (
    echo.
    echo ❌ ADMINISTRATOR RIGHTS REQUIRED
    echo.
    echo ========================================
    echo  SOLUTION REQUIRED
    echo ========================================
    echo.
    echo This script needs administrator privileges to install Python.
    echo.
    echo STEPS TO FIX:
    echo 1. Close this window
    echo 2. Right-click this file: INSTALL_PYTHON_AND_DEPENDENCIES.bat
    echo 3. Select "Run as administrator"
    echo 4. Click "Yes" when prompted
    echo.
    echo ALTERNATIVE OPTIONS:
    echo 1. Install Python manually from https://python.org
    echo 2. Then use SIMPLE_START.bat instead
    echo.
    set "ERROR_OCCURRED=1"
    goto :error_exit
)

echo ✅ Administrator privileges confirmed
echo.

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Python not found. Installing Python 3.11...
    goto :install_python
) else (
    echo Python found. Checking version...
    for /f "tokens=2" %%i in ('python --version 2^>^&1') do set PYTHON_VERSION=%%i
    echo Current Python version: !PYTHON_VERSION!

    REM Check if version is 3.8 or higher
    for /f "tokens=1,2 delims=." %%a in ("!PYTHON_VERSION!") do (
        if %%a geq 3 (
            if %%b geq 8 (
                echo Python version is compatible.
                goto :install_dependencies
            )
        )
    )
    echo Python version is too old. Installing Python 3.11...
    goto :install_python
)

:install_python
echo.
echo ========================================
echo  Installing Python 3.11
echo ========================================
echo.

REM Try multiple download methods
echo Attempting to download Python 3.11 installer...

REM Method 1: PowerShell with TLS 1.2
powershell -Command "[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; Invoke-WebRequest -Uri 'https://www.python.org/ftp/python/3.11.9/python-3.11.9-amd64.exe' -OutFile 'python_installer.exe'"

REM Check if download succeeded
if not exist python_installer.exe (
    echo First download method failed. Trying alternative...

    REM Method 2: Alternative URL
    powershell -Command "[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; Invoke-WebRequest -Uri 'https://www.python.org/ftp/python/3.11.8/python-3.11.8-amd64.exe' -OutFile 'python_installer.exe'"
)

REM Check if download succeeded
if not exist python_installer.exe (
    echo.
    echo ERROR: Failed to download Python installer.
    echo.
    echo This could be due to:
    echo - No internet connection
    echo - Corporate firewall blocking downloads
    echo - Antivirus software interference
    echo.
    echo MANUAL INSTALLATION REQUIRED:
    echo 1. Go to https://python.org/downloads/
    echo 2. Download Python 3.11 or newer
    echo 3. Install with "Add Python to PATH" checked
    echo 4. Restart this script
    echo.
    pause
    exit /b 1
)

echo Installing Python 3.11...
echo This may take a few minutes...

REM Try interactive installation first (more reliable)
echo Attempting interactive installation...
python_installer.exe InstallAllUsers=1 PrependPath=1 Include_test=0

REM Wait for installation to complete
echo Waiting for installation to complete...
timeout /t 15 /nobreak >nul

REM Clean up installer
if exist python_installer.exe del python_installer.exe

REM Refresh PATH environment variable
echo Refreshing environment variables...
for /f "tokens=2*" %%a in ('reg query "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Environment" /v PATH') do set "SYSTEM_PATH=%%b"
for /f "tokens=2*" %%a in ('reg query "HKCU\Environment" /v PATH 2^>nul') do set "USER_PATH=%%b"
set "PATH=%SYSTEM_PATH%;%USER_PATH%"

REM Verify installation with multiple attempts
echo Verifying Python installation...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo First verification failed, trying py launcher...
    py --version >nul 2>&1
    if %errorlevel% neq 0 (
        echo.
        echo ERROR: Python installation verification failed.
        echo.
        echo TROUBLESHOOTING STEPS:
        echo 1. Restart your computer
        echo 2. Try running this script again
        echo 3. Or install Python manually from https://python.org
        echo    - Make sure to check "Add Python to PATH"
        echo.
        pause
        exit /b 1
    ) else (
        echo Python installed successfully! (using py launcher)
        set PYTHON_CMD=py
    )
) else (
    echo Python installed successfully!
    set PYTHON_CMD=python
)

goto :install_dependencies

:install_dependencies
echo.
echo ========================================
echo  Installing Dependencies
echo ========================================
echo.

REM Set Python command if not already set
if not defined PYTHON_CMD set PYTHON_CMD=python

REM Upgrade pip first
echo Upgrading pip...
%PYTHON_CMD% -m pip install --upgrade pip

if %errorlevel% neq 0 (
    echo Pip upgrade failed, trying alternative...
    %PYTHON_CMD% -m ensurepip --upgrade
)

REM Install dependencies with better error handling
echo Installing required packages...
echo This may take several minutes...

echo Installing from requirements.txt...
%PYTHON_CMD% -m pip install -r requirements.txt --user

REM Verify critical packages are installed
echo.
echo Verifying critical packages...
%PYTHON_CMD% -c "import streamlit; print('✅ Streamlit installed')" 2>nul || echo "⚠️ Streamlit installation issue"
%PYTHON_CMD% -c "import torch; print('✅ PyTorch installed')" 2>nul || echo "⚠️ PyTorch installation issue"
%PYTHON_CMD% -c "import shap; print('✅ SHAP installed')" 2>nul || echo "⚠️ SHAP installation issue"
%PYTHON_CMD% -c "import nilearn; print('✅ Nilearn installed')" 2>nul || echo "⚠️ Nilearn installation issue"

REM Verify critical packages
echo Verifying installation...
%PYTHON_CMD% -c "import streamlit; print('Streamlit: OK')" 2>nul
if %errorlevel% neq 0 (
    echo WARNING: Streamlit installation may have failed
    echo Trying alternative installation...
    %PYTHON_CMD% -m pip install -r requirements.txt --user
)

%PYTHON_CMD% -c "import torch; print('PyTorch: OK')" 2>nul
if %errorlevel% neq 0 (
    echo WARNING: PyTorch installation may have failed
    echo This is often due to network issues. The demo may still work.
)

echo.
echo ========================================
echo  Installation Complete!
echo ========================================
echo.
echo ✅ Dependencies installation finished successfully!
echo.
echo NEXT STEPS:
echo 1. Close this window
echo 2. Double-click "RUN_DEMO.bat" to start the demo
echo.
echo The demo will open in your web browser at:
echo http://localhost:8501
echo.
echo If you encounter issues, check TROUBLESHOOTING.md
echo.
goto :normal_exit

REM ========================================
REM  ERROR HANDLING AND EXIT ROUTINES
REM ========================================

:error_exit
echo.
echo ========================================
echo  INSTALLATION FAILED
echo ========================================
echo.
echo The installation encountered an error and cannot continue.
echo.
echo TROUBLESHOOTING STEPS:
echo 1. Check the error messages above
echo 2. Ensure you're running as Administrator
echo 3. Check your internet connection
echo 4. Temporarily disable antivirus software
echo 5. Try installing Python manually from https://python.org
echo 6. Check TROUBLESHOOTING.md for detailed solutions
echo.
echo Press any key to exit...
pause >nul
exit /b 1

:normal_exit
echo.
echo ========================================
echo  INSTALLATION SUCCESSFUL
echo ========================================
echo.
echo Python and all dependencies have been installed successfully!
echo You can now run the demo using RUN_DEMO.bat
echo.
echo Press any key to exit...
pause >nul
exit /b 0

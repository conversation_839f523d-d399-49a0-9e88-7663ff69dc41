@echo off
setlocal enabledelayedexpansion

REM ========================================
REM  CORRECTED DEPLOYMENT VERIFICATION
REM ========================================

cd /d "%~dp0"

title Demetify Demo - Deployment Verification

echo ========================================
echo  CORRECTED DEPLOYMENT VERIFICATION
echo ========================================
echo.
echo This script verifies the corrected AD vs CN frontend
echo deployment is working properly.
echo.
echo Working directory: %CD%
echo Current time: %TIME%
echo.

set "VERIFICATION_PASSED=1"

REM ========================================
REM  TEST 1: CORRECT FRONTEND FILES
REM ========================================

echo [TEST 1] Verifying correct frontend files...

if not exist "demetify_ncomms2022_app.py" (
    echo ❌ demetify_ncomms2022_app.py missing
    set "VERIFICATION_PASSED=0"
) else (
    echo ✅ demetify_ncomms2022_app.py found
)

if not exist "ncomms2022_model_enhanced.py" (
    echo ❌ ncomms2022_model_enhanced.py missing
    set "VERIFICATION_PASSED=0"
) else (
    echo ✅ ncomms2022_model_enhanced.py found
)

if not exist "ncomms2022_preprocessing_fsl.py" (
    echo ❌ ncomms2022_preprocessing_fsl.py missing
    set "VERIFICATION_PASSED=0"
) else (
    echo ✅ ncomms2022_preprocessing_fsl.py found
)

if not exist "ncomms2022_shap.py" (
    echo ❌ ncomms2022_shap.py missing
    set "VERIFICATION_PASSED=0"
) else (
    echo ✅ ncomms2022_shap.py found
)

echo.

REM ========================================
REM  TEST 2: INCORRECT FILES REMOVED
REM ========================================

echo [TEST 2] Verifying incorrect files were removed...

if exist "cn_mci_ad_frontend.py" (
    echo ❌ cn_mci_ad_frontend.py still present (should be removed)
    set "VERIFICATION_PASSED=0"
) else (
    echo ✅ cn_mci_ad_frontend.py correctly removed
)

if exist "cn_mci_ad_model.py" (
    echo ❌ cn_mci_ad_model.py still present (should be removed)
    set "VERIFICATION_PASSED=0"
) else (
    echo ✅ cn_mci_ad_model.py correctly removed
)

if exist "memory_efficient_cnn_model.pth" (
    echo ❌ memory_efficient_cnn_model.pth still present (should be removed)
    set "VERIFICATION_PASSED=0"
) else (
    echo ✅ memory_efficient_cnn_model.pth correctly removed
)

echo.

REM ========================================
REM  TEST 3: MODEL FILES
REM ========================================

echo [TEST 3] Verifying model files...

if not exist "ncomms2022_original" (
    echo ❌ ncomms2022_original directory missing
    set "VERIFICATION_PASSED=0"
) else (
    echo ✅ ncomms2022_original directory found
    
    if not exist "ncomms2022_original\checkpoint_dir\CNN_baseline_new_cross0" (
        echo ❌ Model checkpoint directory missing
        set "VERIFICATION_PASSED=0"
    ) else (
        echo ✅ Model checkpoint directory found
        
        REM Count model files
        set "MODEL_COUNT=0"
        for %%f in ("ncomms2022_original\checkpoint_dir\CNN_baseline_new_cross0\*.pth") do (
            set /a MODEL_COUNT+=1
        )
        
        if !MODEL_COUNT! geq 3 (
            echo ✅ Model weight files found (!MODEL_COUNT! files)
        ) else (
            echo ❌ Insufficient model weight files (!MODEL_COUNT! found, need 3)
            set "VERIFICATION_PASSED=0"
        )
    )
)

echo.

REM ========================================
REM  TEST 4: DEPENDENCIES
REM ========================================

echo [TEST 4] Verifying dependencies...

if not exist "requirements.txt" (
    echo ❌ requirements.txt missing
    set "VERIFICATION_PASSED=0"
) else (
    echo ✅ requirements.txt found
)

if exist "requirements_cn_mci_ad.txt" (
    echo ❌ requirements_cn_mci_ad.txt still present (should be removed)
    set "VERIFICATION_PASSED=0"
) else (
    echo ✅ requirements_cn_mci_ad.txt correctly removed
)

echo.

REM ========================================
REM  TEST 5: LAUNCHER SCRIPTS
REM ========================================

echo [TEST 5] Verifying launcher scripts are updated...

REM Check if RUN_DEMO.bat references correct files
findstr "demetify_ncomms2022_app.py" "RUN_DEMO.bat" >nul
if !errorlevel! equ 0 (
    echo ✅ RUN_DEMO.bat updated for correct frontend
) else (
    echo ❌ RUN_DEMO.bat not updated for correct frontend
    set "VERIFICATION_PASSED=0"
)

REM Check if SIMPLE_START.bat references correct files
findstr "demetify_ncomms2022_app.py" "SIMPLE_START.bat" >nul
if !errorlevel! equ 0 (
    echo ✅ SIMPLE_START.bat updated for correct frontend
) else (
    echo ❌ SIMPLE_START.bat not updated for correct frontend
    set "VERIFICATION_PASSED=0"
)

echo.

REM ========================================
REM  TEST 6: DOCUMENTATION
REM ========================================

echo [TEST 6] Verifying documentation is updated...

if exist "README.md" (
    findstr "AD vs CN" "README.md" >nul
    if !errorlevel! equ 0 (
        echo ✅ README.md updated for AD vs CN classification
    ) else (
        echo ❌ README.md not updated for AD vs CN classification
        set "VERIFICATION_PASSED=0"
    )
) else (
    echo ❌ README.md missing
    set "VERIFICATION_PASSED=0"
)

if exist "DEPLOYMENT_CORRECTION.md" (
    echo ✅ DEPLOYMENT_CORRECTION.md found
) else (
    echo ❌ DEPLOYMENT_CORRECTION.md missing
    set "VERIFICATION_PASSED=0"
)

echo.

REM ========================================
REM  VERIFICATION RESULTS
REM ========================================

echo ========================================
echo  VERIFICATION RESULTS
echo ========================================
echo.

if !VERIFICATION_PASSED! equ 1 (
    echo 🎉 ALL VERIFICATION TESTS PASSED!
    echo.
    echo ✅ Correct AD vs CN frontend deployed
    echo ✅ Incorrect CN/MCI/AD files removed
    echo ✅ NCOMMS2022 model files present
    echo ✅ Dependencies updated
    echo ✅ Launcher scripts corrected
    echo ✅ Documentation updated
    echo.
    echo The deployment correction is COMPLETE and SUCCESSFUL!
    echo Professors can now use the correct AD vs CN demo.
    echo.
    echo NEXT STEPS:
    echo 1. Test with RUN_DEMO.bat
    echo 2. Verify browser opens with AD vs CN interface
    echo 3. Upload MRI scan to test binary classification
    echo.
) else (
    echo ❌ VERIFICATION FAILED!
    echo.
    echo Some issues were found with the deployment correction.
    echo Please review the error messages above and fix the issues.
    echo.
)

echo Press any key to exit...
pause >nul

if !VERIFICATION_PASSED! equ 1 (
    exit /b 0
) else (
    exit /b 1
)

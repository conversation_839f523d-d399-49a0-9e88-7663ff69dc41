@echo off
setlocal enabledelayedexpansion

REM ========================================
REM  BULLETPROOF SIMPLE LAUNCHER
REM ========================================

REM Trap all errors and ensure window stays open
set "ERROR_OCCURRED=0"

REM CRITICAL: Change to the directory where this batch file is located
echo Changing to script directory...
cd /d "%~dp0"
if %errorlevel% neq 0 (
    echo ERROR: Failed to change to script directory
    echo Script location: %~dp0
    set "ERROR_OCCURRED=1"
    goto :error_exit
)

title Demetify CN/MCI/AD Demo - Simple Start

echo ========================================
echo  Demetify CN/MCI/AD Demo - Simple Start
echo ========================================
echo.
echo Script location: %~dp0
echo Working directory: %CD%
echo Current time: %TIME%
echo.
echo This launcher assumes Python is already installed.
echo If you need to install Python, use RUN_DEMO.bat instead.
echo.

REM Verify demo files exist
echo Checking for demo files...
if not exist "demetify_ncomms2022_app.py" (
    echo ❌ ERROR: demetify_ncomms2022_app.py not found
    echo Current directory: %CD%
    echo.
    echo SOLUTION: Ensure all demo files are in the same folder
    set "ERROR_OCCURRED=1"
    goto :error_exit
)

if not exist "requirements.txt" (
    echo ❌ ERROR: requirements.txt not found
    echo Current directory: %CD%
    set "ERROR_OCCURRED=1"
    goto :error_exit
)

echo ✅ Demo files found
echo.

REM ========================================
REM  PYTHON DETECTION
REM ========================================

echo Checking for Python installation...
set "PYTHON_CMD="
set "PYTHON_FOUND=0"

echo Testing 'python' command...
python --version >nul 2>&1
if !errorlevel! equ 0 (
    for /f "tokens=2" %%v in ('python --version 2^>^&1') do (
        echo ✅ Found: python %%v
        set "PYTHON_CMD=python"
        set "PYTHON_FOUND=1"
        goto :python_detected
    )
)

echo Testing 'py' command...
py --version >nul 2>&1
if !errorlevel! equ 0 (
    for /f "tokens=2" %%v in ('py --version 2^>^&1') do (
        echo ✅ Found: py %%v
        set "PYTHON_CMD=py"
        set "PYTHON_FOUND=1"
        goto :python_detected
    )
)

echo Testing 'python3' command...
python3 --version >nul 2>&1
if !errorlevel! equ 0 (
    for /f "tokens=2" %%v in ('python3 --version 2^>^&1') do (
        echo ✅ Found: python3 %%v
        set "PYTHON_CMD=python3"
        set "PYTHON_FOUND=1"
        goto :python_detected
    )
)

echo ❌ ERROR: No Python installation found
echo.
echo SOLUTIONS:
echo 1. Use RUN_DEMO.bat for automatic Python installation
echo 2. Install Python manually from https://python.org
echo 3. Ensure Python is added to PATH during installation
echo.
set "ERROR_OCCURRED=1"
goto :error_exit

:python_detected
echo.
echo ✅ Using Python: !PYTHON_CMD!
echo Current directory: %CD%
echo.

REM ========================================
REM  DEPENDENCY INSTALLATION
REM ========================================

echo Installing/updating dependencies...
echo Command: !PYTHON_CMD! -m pip install -r requirements.txt --user --quiet
echo.

!PYTHON_CMD! -m pip install -r requirements.txt --user --quiet
set "PIP_RESULT=!errorlevel!"

if !PIP_RESULT! neq 0 (
    echo ❌ WARNING: Dependency installation may have failed (Error code: !PIP_RESULT!)
    echo Continuing anyway - some packages might already be installed
    echo.
) else (
    echo ✅ Dependencies installed successfully
    echo.
)

REM ========================================
REM  START DEMO
REM ========================================

echo ========================================
echo  STARTING DEMETIFY DEMO
echo ========================================
echo.
echo Starting Streamlit server...
echo Command: !PYTHON_CMD! -m streamlit run demetify_ncomms2022_app.py
echo.

REM Start Streamlit in background without automatic browser opening
start "Demetify Demo Server" !PYTHON_CMD! -m streamlit run demetify_ncomms2022_app.py --server.port 8501 --server.address localhost --browser.gatherUsageStats false --server.headless true

REM Wait for Streamlit to initialize
echo Waiting for Streamlit to start...
timeout /t 3 /nobreak >nul

echo.
echo ========================================
echo  LAUNCHING BROWSER
echo ========================================
echo.

REM Launch browser using our robust browser launcher
if exist "launch_browser.bat" (
    call "launch_browser.bat" "http://localhost:8501"
    set "BROWSER_RESULT=!errorlevel!"

    if !BROWSER_RESULT! neq 0 (
        echo.
        echo ⚠️  Automatic browser launch failed
        goto :manual_browser_instructions
    )
) else (
    echo ⚠️  Browser launcher not found
    goto :manual_browser_instructions
)

echo.
echo ========================================
echo  DEMO IS RUNNING
echo ========================================
echo.
echo ✅ Demo is running at: http://localhost:8501
echo ✅ Browser should have opened automatically
echo.
echo The demo will continue running until you:
echo 1. Close the browser tab, OR
echo 2. Press Ctrl+C in the server window, OR
echo 3. Press any key in this window
echo.
echo Press any key to stop the demo...
pause >nul

REM Stop the Streamlit server
echo.
echo Stopping demo server...
taskkill /F /IM python.exe /FI "WINDOWTITLE eq Demetify Demo Server" >nul 2>&1
echo ✅ Demo stopped
goto :normal_exit

:manual_browser_instructions
echo.
echo ========================================
echo  MANUAL BROWSER INSTRUCTIONS
echo ========================================
echo.
echo Please manually open your web browser and navigate to:
echo.
echo     http://localhost:8501
echo.
echo STEPS:
echo 1. Open Chrome, Firefox, Edge, or any modern browser
echo 2. Click in the address bar
echo 3. Type: http://localhost:8501
echo 4. Press Enter
echo.
echo ✅ The Demetify CN/MCI/AD Demo will then load
echo.
echo Press any key when you're finished with the demo...
pause >nul

REM Stop the Streamlit server
echo.
echo Stopping demo server...
taskkill /F /IM python.exe /FI "WINDOWTITLE eq Demetify Demo Server" >nul 2>&1
echo ✅ Demo stopped
goto :normal_exit

REM ========================================
REM  ERROR HANDLING AND EXIT ROUTINES
REM ========================================

:error_exit
echo.
echo ========================================
echo  ERROR OCCURRED
echo ========================================
echo.
echo The simple launcher encountered an error.
echo.
echo TROUBLESHOOTING:
echo 1. Check error messages above
echo 2. Try RUN_DEMO.bat for automatic setup
echo 3. Run DIAGNOSE_SYSTEM.bat for system analysis
echo 4. Check TROUBLESHOOTING.md for solutions
echo.
echo Press any key to exit...
pause >nul
exit /b 1

:normal_exit
echo.
echo ========================================
echo  DEMO SESSION ENDED
echo ========================================
echo.
echo Thank you for using Demetify CN/MCI/AD Demo!
echo.
echo Press any key to exit...
pause >nul
exit /b 0

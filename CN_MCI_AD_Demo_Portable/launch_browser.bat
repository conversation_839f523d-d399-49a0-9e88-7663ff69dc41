@echo off
setlocal enabledelayedexpansion

REM ========================================
REM  ROBUST BROWSER LAUNCHER
REM ========================================

REM Change to script directory
cd /d "%~dp0"

REM Default URL
set "DEMO_URL=http://localhost:8501"
if not "%~1"=="" set "DEMO_URL=%~1"

echo ========================================
echo  LAUNCHING BROWSER FOR DEMETIFY DEMO
echo ========================================
echo.
echo Target URL: %DEMO_URL%
echo.

REM ========================================
REM  METHOD 1: Windows Default Browser
REM ========================================

echo [Method 1] Trying Windows default browser...
start "" "%DEMO_URL%" >nul 2>&1
if !errorlevel! equ 0 (
    echo ✅ Successfully launched default browser
    echo.
    echo The demo should now be opening in your browser.
    echo If it doesn't appear, please wait a few seconds.
    echo.
    goto :success
)

echo ❌ Default browser launch failed

REM ========================================
REM  METHOD 2: Microsoft Edge
REM ========================================

echo.
echo [Method 2] Trying Microsoft Edge...

REM Try Edge (new Chromium-based)
where msedge >nul 2>&1
if !errorlevel! equ 0 (
    echo Found: Microsoft Edge (Chromium)
    start "" msedge "%DEMO_URL%" >nul 2>&1
    if !errorlevel! equ 0 (
        echo ✅ Successfully launched Microsoft Edge
        goto :success
    )
)

REM Try Edge (legacy)
if exist "C:\Program Files (x86)\Microsoft\Edge\Application\msedge.exe" (
    echo Found: Microsoft Edge (Legacy path)
    start "" "C:\Program Files (x86)\Microsoft\Edge\Application\msedge.exe" "%DEMO_URL%" >nul 2>&1
    if !errorlevel! equ 0 (
        echo ✅ Successfully launched Microsoft Edge
        goto :success
    )
)

echo ❌ Microsoft Edge launch failed

REM ========================================
REM  METHOD 3: Google Chrome
REM ========================================

echo.
echo [Method 3] Trying Google Chrome...

REM Try Chrome command
where chrome >nul 2>&1
if !errorlevel! equ 0 (
    echo Found: Google Chrome (command)
    start "" chrome "%DEMO_URL%" >nul 2>&1
    if !errorlevel! equ 0 (
        echo ✅ Successfully launched Google Chrome
        goto :success
    )
)

REM Try Chrome executable paths
if exist "C:\Program Files\Google\Chrome\Application\chrome.exe" (
    echo Found: Google Chrome (Program Files)
    start "" "C:\Program Files\Google\Chrome\Application\chrome.exe" "%DEMO_URL%" >nul 2>&1
    if !errorlevel! equ 0 (
        echo ✅ Successfully launched Google Chrome
        goto :success
    )
)

if exist "C:\Program Files (x86)\Google\Chrome\Application\chrome.exe" (
    echo Found: Google Chrome (Program Files x86)
    start "" "C:\Program Files (x86)\Google\Chrome\Application\chrome.exe" "%DEMO_URL%" >nul 2>&1
    if !errorlevel! equ 0 (
        echo ✅ Successfully launched Google Chrome
        goto :success
    )
)

echo ❌ Google Chrome launch failed

REM ========================================
REM  METHOD 4: Mozilla Firefox
REM ========================================

echo.
echo [Method 4] Trying Mozilla Firefox...

REM Try Firefox command
where firefox >nul 2>&1
if !errorlevel! equ 0 (
    echo Found: Mozilla Firefox (command)
    start "" firefox "%DEMO_URL%" >nul 2>&1
    if !errorlevel! equ 0 (
        echo ✅ Successfully launched Mozilla Firefox
        goto :success
    )
)

REM Try Firefox executable paths
if exist "C:\Program Files\Mozilla Firefox\firefox.exe" (
    echo Found: Mozilla Firefox (Program Files)
    start "" "C:\Program Files\Mozilla Firefox\firefox.exe" "%DEMO_URL%" >nul 2>&1
    if !errorlevel! equ 0 (
        echo ✅ Successfully launched Mozilla Firefox
        goto :success
    )
)

if exist "C:\Program Files (x86)\Mozilla Firefox\firefox.exe" (
    echo Found: Mozilla Firefox (Program Files x86)
    start "" "C:\Program Files (x86)\Mozilla Firefox\firefox.exe" "%DEMO_URL%" >nul 2>&1
    if !errorlevel! equ 0 (
        echo ✅ Successfully launched Mozilla Firefox
        goto :success
    )
)

echo ❌ Mozilla Firefox launch failed

REM ========================================
REM  METHOD 5: Internet Explorer (Fallback)
REM ========================================

echo.
echo [Method 5] Trying Internet Explorer (fallback)...

if exist "C:\Program Files\Internet Explorer\iexplore.exe" (
    echo Found: Internet Explorer
    start "" "C:\Program Files\Internet Explorer\iexplore.exe" "%DEMO_URL%" >nul 2>&1
    if !errorlevel! equ 0 (
        echo ✅ Successfully launched Internet Explorer
        echo.
        echo NOTE: Internet Explorer may not display the demo correctly.
        echo For best experience, please install Chrome, Firefox, or Edge.
        goto :success
    )
)

echo ❌ Internet Explorer launch failed

REM ========================================
REM  ALL METHODS FAILED
REM ========================================

echo.
echo ❌ ALL BROWSER LAUNCH METHODS FAILED
echo.
echo ========================================
echo  MANUAL BROWSER INSTRUCTIONS
echo ========================================
echo.
echo Please manually open your web browser and navigate to:
echo.
echo     %DEMO_URL%
echo.
echo STEPS:
echo 1. Open any web browser (Chrome, Firefox, Edge, etc.)
echo 2. Click in the address bar
echo 3. Type: %DEMO_URL%
echo 4. Press Enter
echo.
echo The Demetify CN/MCI/AD Demo should then load.
echo.
goto :manual_instructions

:success
echo.
echo ========================================
echo  BROWSER LAUNCHED SUCCESSFULLY
echo ========================================
echo.
echo The Demetify CN/MCI/AD Demo should now be loading in your browser.
echo.
echo If the browser opened but the page doesn't load:
echo 1. Wait a few seconds for Streamlit to start
echo 2. Refresh the page (F5 or Ctrl+R)
echo 3. Check that the URL is: %DEMO_URL%
echo.
exit /b 0

:manual_instructions
echo If you're having trouble, try these browsers:
echo.
echo • Microsoft Edge (recommended for Windows)
echo • Google Chrome
echo • Mozilla Firefox
echo.
echo The demo works best with modern browsers.
echo.
exit /b 1

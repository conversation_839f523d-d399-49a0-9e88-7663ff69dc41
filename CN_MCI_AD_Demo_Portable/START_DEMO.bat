@echo off
setlocal

REM CRITICAL: Change to the directory where this batch file is located
cd /d "%~dp0"

echo ========================================
echo  Demetify CN/MCI/AD Demo Launcher
echo ========================================
echo.
echo Working directory: %CD%
echo.

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Python is not installed or not in PATH.
    echo.
    echo Please run "INSTALL_PYTHON_AND_DEPENDENCIES.bat" first
    echo to automatically install Python and all dependencies.
    echo.
    pause
    exit /b 1
)

REM Check if required files exist
if not exist "cn_mci_ad_frontend.py" (
    echo ERROR: Frontend file not found.
    echo Please ensure all demo files are in the same folder.
    pause
    exit /b 1
)

if not exist "cn_mci_ad_model.py" (
    echo ERROR: Model file not found.
    echo Please ensure all demo files are in the same folder.
    pause
    exit /b 1
)

REM Check if model weights exist
if exist "memory_efficient_cnn_model.pth" (
    echo Model weights found - Full demo mode enabled
) else (
    echo WARNING: Model weights not found - Demo will run in simulation mode
)

echo.
echo Starting CN/MCI/AD Classification Demo...
echo.
echo The demo will open in your web browser at:
echo http://localhost:8501
echo.
echo Features:
echo - 3-category classification: CN, MCI, AD
echo - Real-time MRI analysis
echo - Probability visualization
echo - Professional radiologist interface
echo.
echo Press Ctrl+C to stop the demo
echo.

REM Start the Streamlit application
python -m streamlit run cn_mci_ad_frontend.py --server.port 8501 --server.address localhost --browser.gatherUsageStats false

echo.
echo Demo stopped.
pause
